from database import get_db_connection

def verify_seeded_data():
    """Verify the data that was seeded into the database."""
    conn = get_db_connection()
    if not conn:
        return
    
    cursor = conn.cursor()
    
    print("🔍 Verifying seeded data...")
    print("=" * 60)
    
    # Check tenants
    cursor.execute("SELECT COUNT(*) FROM tenant WHERE title LIKE 'Tenant_%'")
    tenant_count = cursor.fetchone()[0]
    print(f"📊 Tenants created: {tenant_count}")
    
    # Check users
    cursor.execute("SELECT COUNT(*) FROM tb_user WHERE email LIKE '<EMAIL>'")
    user_count = cursor.fetchone()[0]
    print(f"👤 Users created: {user_count}")
    
    # Check factories
    cursor.execute("SELECT COUNT(*) FROM config_tenant_factory WHERE factory_name LIKE 'Factory_%'")
    factory_count = cursor.fetchone()[0]
    print(f"🏭 Factories created: {factory_count}")
    
    # Check CAS locations
    cursor.execute("SELECT COUNT(*) FROM config_cas_location WHERE cas_location_name LIKE 'CAS_Location_%'")
    cas_count = cursor.fetchone()[0]
    print(f"📍 CAS Locations created: {cas_count}")
    
    # Check measurement locations
    cursor.execute("SELECT COUNT(*) FROM config_measurement_location WHERE measurement_location_name LIKE 'Measurement_Location_%'")
    measurement_loc_count = cursor.fetchone()[0]
    print(f"📏 Measurement Locations created: {measurement_loc_count}")
    
    # Check measurement points
    cursor.execute("SELECT COUNT(*) FROM config_measurement_point WHERE measurement_point_name LIKE 'Measurement_Point_%'")
    measurement_point_count = cursor.fetchone()[0]
    print(f"📌 Measurement Points created: {measurement_point_count}")
    
    # Check devices/gateways
    cursor.execute("SELECT COUNT(*) FROM device WHERE name LIKE 'Gateway_%'")
    device_count = cursor.fetchone()[0]
    print(f"📡 Devices/Gateways created: {device_count}")
    
    cursor.execute("SELECT COUNT(*) FROM gateway WHERE gateway_name LIKE 'Gateway_%'")
    gateway_count = cursor.fetchone()[0]
    print(f"🌐 Gateway records created: {gateway_count}")
    
    # Check sensors
    cursor.execute("SELECT COUNT(*) FROM master_sensors WHERE sensors_name LIKE 'Sensor_%'")
    sensor_count = cursor.fetchone()[0]
    print(f"🔧 Sensors created: {sensor_count}")
    
    # Check channels
    cursor.execute("SELECT COUNT(*) FROM master_channels WHERE channel_name LIKE 'Channel_%'")
    channel_count = cursor.fetchone()[0]
    print(f"📺 Channels created: {channel_count}")
    
    # Check configuration mappings
    cursor.execute("SELECT COUNT(*) FROM config_tenant_gateway_sensors_channels")
    config_count = cursor.fetchone()[0]
    print(f"⚙️ Configuration mappings created: {config_count}")
    
    # Check history data
    cursor.execute("SELECT COUNT(*) FROM history_report_data")
    history_count = cursor.fetchone()[0]
    print(f"📊 History data points created: {history_count}")
    
    print("=" * 60)
    
    # Show sample data
    print("\n📋 Sample tenant data:")
    cursor.execute("""
        SELECT t.title, t.tenant_no, u.email, u.first_name 
        FROM tenant t 
        JOIN tb_user u ON t.id = u.tenant_id 
        WHERE t.title LIKE 'Tenant_%' 
        LIMIT 5
    """)
    
    tenants = cursor.fetchall()
    for tenant in tenants:
        title, tenant_no, email, first_name = tenant
        print(f"  🏢 {title} (#{tenant_no}) - User: {first_name} ({email})")
    
    print("\n📋 Sample device hierarchy:")
    cursor.execute("""
        SELECT
            g.gateway_name,
            ms.sensors_name,
            mc.channel_name
        FROM gateway g
        JOIN master_sensors ms ON g.id::text = ms.gw
        JOIN master_channels mc ON ms.id = mc.entity_id
        WHERE g.gateway_name LIKE 'Gateway_%'
        LIMIT 10
    """)
    
    devices = cursor.fetchall()
    for device in devices:
        gateway_name, sensor_name, channel_name = device
        print(f"  📡 {gateway_name} → 🔧 {sensor_name} → 📺 {channel_name}")
    
    cursor.close()
    conn.close()
    
    print("\n✅ Data verification completed!")

if __name__ == "__main__":
    verify_seeded_data()
