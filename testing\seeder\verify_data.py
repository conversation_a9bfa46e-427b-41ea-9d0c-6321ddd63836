from database import get_db_connection

def verify_seeded_data():
    """Verify the data that was seeded into the database."""
    conn = get_db_connection()
    if not conn:
        return

    cursor = conn.cursor()

    print("🔍 Verifying seeded data...")
    print("=" * 80)

    # Check tenants
    cursor.execute("SELECT COUNT(*) FROM tenant WHERE title LIKE 'Tenant_%'")
    tenant_count = cursor.fetchone()[0]
    print(f"📊 Tenants created: {tenant_count}")

    # Check users
    cursor.execute("SELECT COUNT(*) FROM tb_user WHERE email LIKE '<EMAIL>'")
    user_count = cursor.fetchone()[0]
    print(f"👤 Users created: {user_count}")

    # Check NEW TABLES
    # Check user credentials
    cursor.execute("SELECT COUNT(*) FROM user_credentials")
    user_creds_count = cursor.fetchone()[0]
    print(f"🔐 User credentials created: {user_creds_count}")

    # Check device profiles
    cursor.execute("SELECT COUNT(*) FROM device_profile WHERE name = 'default'")
    device_profile_count = cursor.fetchone()[0]
    print(f"📄 Device profiles created: {device_profile_count}")

    # Check device credentials
    cursor.execute("SELECT COUNT(*) FROM device_credentials WHERE credentials_type = 'ACCESS_TOKEN'")
    device_creds_count = cursor.fetchone()[0]
    print(f"🔑 Device credentials created: {device_creds_count}")

    # Check licenses
    cursor.execute("SELECT COUNT(*) FROM license_management WHERE status = 'Active'")
    license_count = cursor.fetchone()[0]
    print(f"📜 License management records: {license_count}")

    cursor.execute("SELECT COUNT(*) FROM license_type_management")
    license_type_count = cursor.fetchone()[0]
    print(f"📋 License type management records: {license_type_count}")

    # Check permissions
    cursor.execute("SELECT COUNT(*) FROM config_tb_user_role_permission")
    permission_count = cursor.fetchone()[0]
    print(f"🔐 User role permissions created: {permission_count}")

    # Check ts_kv_dictionary
    cursor.execute("SELECT COUNT(*) FROM ts_kv_dictionary")
    ts_kv_count = cursor.fetchone()[0]
    print(f"📚 TS KV dictionary entries: {ts_kv_count}")

    # Check master_gateway
    cursor.execute("SELECT COUNT(*) FROM master_gateway WHERE g_type = '\"SUTOgateway\"'")
    master_gateway_count = cursor.fetchone()[0]
    print(f"🌐 Master gateway records: {master_gateway_count}")

    print("-" * 40)

    # Check factories
    cursor.execute("SELECT COUNT(*) FROM config_tenant_factory WHERE factory_name LIKE 'Factory_%'")
    factory_count = cursor.fetchone()[0]
    print(f"🏭 Factories created: {factory_count}")

    # Check CAS locations
    cursor.execute("SELECT COUNT(*) FROM config_cas_location WHERE cas_location_name LIKE 'CAS_Location_%'")
    cas_count = cursor.fetchone()[0]
    print(f"📍 CAS Locations created: {cas_count}")

    # Check measurement locations
    cursor.execute("SELECT COUNT(*) FROM config_measurement_location WHERE measurement_location_name LIKE 'Measurement_Location_%'")
    measurement_loc_count = cursor.fetchone()[0]
    print(f"📏 Measurement Locations created: {measurement_loc_count}")

    # Check measurement points
    cursor.execute("SELECT COUNT(*) FROM config_measurement_point WHERE measurement_point_name LIKE 'Measurement_Point_%'")
    measurement_point_count = cursor.fetchone()[0]
    print(f"📌 Measurement Points created: {measurement_point_count}")

    # Check devices/gateways with new naming convention
    cursor.execute("SELECT COUNT(*) FROM device WHERE name LIKE 'S331-%'")
    device_count = cursor.fetchone()[0]
    print(f"📡 Devices/Gateways created: {device_count}")

    cursor.execute("SELECT COUNT(*) FROM gateway WHERE gateway_name LIKE 'S331-%'")
    gateway_count = cursor.fetchone()[0]
    print(f"🌐 Gateway records created: {gateway_count}")

    # Check sensors with new naming convention
    cursor.execute("SELECT COUNT(*) FROM master_sensors WHERE sensors_name LIKE 'S40%/%'")
    sensor_count = cursor.fetchone()[0]
    print(f"🔧 Sensors created: {sensor_count}")

    # Check channels with proper names
    cursor.execute("SELECT COUNT(*) FROM master_channels WHERE channel_name IN ('Concentration', 'Dew point', 'Energy', 'Flow', 'Humidity', 'Mass', 'Power', 'Pressure', 'Temperature', 'Velocity', 'Voltage', 'Volume')")
    channel_count = cursor.fetchone()[0]
    print(f"📺 Channels created: {channel_count}")

    # Check configuration mappings
    cursor.execute("SELECT COUNT(*) FROM config_tenant_gateway_sensors_channels")
    config_count = cursor.fetchone()[0]
    print(f"⚙️ Configuration mappings created: {config_count}")

    # Check history data
    cursor.execute("SELECT COUNT(*) FROM history_report_data")
    history_count = cursor.fetchone()[0]
    print(f"📊 History data points created: {history_count}")

    # Check rule chains
    cursor.execute("SELECT COUNT(*) FROM rule_chain WHERE name = 'Root Rule Chain'")
    rule_chain_count = cursor.fetchone()[0]
    print(f"🔗 Rule chains created: {rule_chain_count}")

    # Check rule nodes
    cursor.execute("SELECT COUNT(*) FROM rule_node")
    rule_node_count = cursor.fetchone()[0]
    print(f"📋 Rule nodes created: {rule_node_count}")

    # Check relations
    cursor.execute("SELECT COUNT(*) FROM relation WHERE relation_type_group = 'RULE_CHAIN'")
    rule_chain_relations = cursor.fetchone()[0]
    print(f"🔄 Rule chain relations created: {rule_chain_relations}")

    cursor.execute("SELECT COUNT(*) FROM relation WHERE relation_type_group = 'RULE_NODE'")
    rule_node_relations = cursor.fetchone()[0]
    print(f"🔄 Rule node relations created: {rule_node_relations}")

    print("=" * 80)

    # Show sample data
    print("\n📋 Sample tenant data:")
    cursor.execute("""
        SELECT t.title, t.tenant_no, u.email, u.first_name
        FROM tenant t
        JOIN tb_user u ON t.id = u.tenant_id
        WHERE t.title LIKE 'Tenant_%'
        LIMIT 5
    """)

    tenants = cursor.fetchall()
    for tenant in tenants:
        title, tenant_no, email, first_name = tenant
        print(f"  🏢 {title} (#{tenant_no}) - User: {first_name} ({email})")

    print("\n📋 Sample device hierarchy with new naming:")
    cursor.execute("""
        SELECT
            d.name as device_name,
            d.model,
            d.sn,
            ms.sensors_name,
            mc.channel_name,
            mc.unit_id,
            tkv.key_id
        FROM device d
        JOIN master_sensors ms ON d.id = ms.entity_id
        JOIN master_channels mc ON d.id = mc.entity_id
        LEFT JOIN ts_kv_dictionary tkv ON ms.sensors_name = tkv.key
        WHERE d.name LIKE 'S331-%'
        ORDER BY d.name, ms.sensors_name, mc.channel_name
        LIMIT 10
    """)

    devices = cursor.fetchall()
    for device in devices:
        device_name, model, sn, sensor_name, channel_name, unit_id, key_id = device
        print(f"  📡 {device_name} ({model}-{sn}) → 🔧 {sensor_name} (key_id:{key_id}) → 📺 {channel_name} ({unit_id})")

    print("\n📋 Sample permissions:")
    cursor.execute("""
        SELECT u.email, p.feature_list, p.role, p.role_type
        FROM config_tb_user_role_permission p
        JOIN tb_user u ON p.tb_user_id = u.id
        LIMIT 5
    """)

    permissions = cursor.fetchall()
    for perm in permissions:
        email, feature_list, role, role_type = perm
        print(f"  🔐 {email} - {feature_list}: {role} ({role_type})")

    print("\n📋 Sample history data consistency:")
    cursor.execute("""
        SELECT
            h.sensor_name,
            h.channel_name,
            h.key_id,
            COUNT(*) as data_points
        FROM history_report_data h
        GROUP BY h.sensor_name, h.channel_name, h.key_id
        LIMIT 5
    """)

    history_data = cursor.fetchall()
    for hist in history_data:
        sensor_name, channel_name, key_id, data_points = hist
        print(f"  📊 {sensor_name} → {channel_name} (key_id:{key_id}): {data_points} data points")

    print("\n📋 Sample rule chains and nodes:")
    cursor.execute("""
        SELECT
            rc.name as rule_chain_name,
            rc.type as rule_chain_type,
            COUNT(rn.id) as node_count
        FROM rule_chain rc
        LEFT JOIN rule_node rn ON rc.id = rn.rule_chain_id
        WHERE rc.name = 'Root Rule Chain'
        GROUP BY rc.id, rc.name, rc.type
        LIMIT 5
    """)

    rule_chains = cursor.fetchall()
    for rc in rule_chains:
        rule_chain_name, rule_chain_type, node_count = rc
        print(f"  🔗 {rule_chain_name} ({rule_chain_type}): {node_count} nodes")

    print("\n📋 Sample rule node types:")
    cursor.execute("""
        SELECT
            rn.name,
            rn.type,
            COUNT(*) as count
        FROM rule_node rn
        GROUP BY rn.name, rn.type
        ORDER BY rn.name
        LIMIT 7
    """)

    rule_nodes = cursor.fetchall()
    for rn in rule_nodes:
        node_name, node_type, count = rn
        print(f"  📋 {node_name}: {count} instances")

    print("\n📋 Sample rule relations:")
    cursor.execute("""
        SELECT
            r.from_type,
            r.to_type,
            r.relation_type_group,
            r.relation_type,
            COUNT(*) as count
        FROM relation r
        WHERE r.relation_type_group IN ('RULE_CHAIN', 'RULE_NODE')
        GROUP BY r.from_type, r.to_type, r.relation_type_group, r.relation_type
        ORDER BY r.relation_type_group, r.relation_type
        LIMIT 10
    """)

    relations = cursor.fetchall()
    for rel in relations:
        from_type, to_type, rel_group, rel_type, count = rel
        print(f"  🔄 {from_type} → {to_type} ({rel_group}:{rel_type}): {count} relations")

    cursor.close()
    conn.close()

    print("\n✅ Data verification completed!")

def verify_data_consistency():
    """Verify data consistency and foreign key relationships."""
    conn = get_db_connection()
    if not conn:
        return

    cursor = conn.cursor()

    print("\n🔍 Verifying data consistency...")
    print("=" * 80)

    # Check if all devices have credentials
    cursor.execute("""
        SELECT COUNT(*) FROM device d
        LEFT JOIN device_credentials dc ON d.id = dc.device_id
        WHERE dc.device_id IS NULL AND d.name LIKE 'S331-%'
    """)
    missing_device_creds = cursor.fetchone()[0]
    print(f"❌ Devices missing credentials: {missing_device_creds}")

    # Check if all users have credentials
    cursor.execute("""
        SELECT COUNT(*) FROM tb_user u
        LEFT JOIN user_credentials uc ON u.id = uc.user_id
        WHERE uc.user_id IS NULL AND u.email LIKE '<EMAIL>'
    """)
    missing_user_creds = cursor.fetchone()[0]
    print(f"❌ Users missing credentials: {missing_user_creds}")

    # Check if all sensors have ts_kv_dictionary entries
    cursor.execute("""
        SELECT COUNT(*) FROM master_sensors ms
        LEFT JOIN ts_kv_dictionary tkv ON ms.sensors_name = tkv.key
        WHERE tkv.key IS NULL AND ms.sensors_name LIKE 'S40%/%'
    """)
    missing_ts_kv = cursor.fetchone()[0]
    print(f"❌ Sensors missing ts_kv_dictionary entries: {missing_ts_kv}")

    # Check entity_id consistency in master_sensors
    cursor.execute("""
        SELECT COUNT(*) FROM master_sensors ms
        JOIN device d ON ms.entity_id = d.id
        WHERE ms.sensors_name LIKE 'S40%/%'
    """)
    consistent_sensors = cursor.fetchone()[0]
    print(f"✅ Sensors with consistent entity_id: {consistent_sensors}")

    # Check entity_id consistency in master_channels
    cursor.execute("""
        SELECT COUNT(*) FROM master_channels mc
        JOIN device d ON mc.entity_id = d.id
        WHERE mc.channel_name IN ('Concentration', 'Dew point', 'Energy', 'Flow', 'Humidity', 'Mass', 'Power', 'Pressure', 'Temperature', 'Velocity', 'Voltage', 'Volume')
    """)
    consistent_channels = cursor.fetchone()[0]
    print(f"✅ Channels with consistent entity_id: {consistent_channels}")

    cursor.close()
    conn.close()

    print("✅ Data consistency check completed!")

if __name__ == "__main__":
    verify_seeded_data()
    verify_data_consistency()
