from database import get_db_connection

def check_table_schema(table_name):
    """Check the schema of a specific table."""
    conn = get_db_connection()
    if not conn:
        return
    
    cursor = conn.cursor()
    
    cursor.execute("""
        SELECT column_name, data_type, is_nullable, column_default
        FROM information_schema.columns 
        WHERE table_schema = 'public' 
        AND table_name = %s
        ORDER BY ordinal_position;
    """, (table_name,))
    
    columns = cursor.fetchall()
    
    print(f"\n📋 Schema for table '{table_name}':")
    print("-" * 80)
    print(f"{'Column Name':<25} {'Data Type':<20} {'Nullable':<10} {'Default'}")
    print("-" * 80)
    
    for column in columns:
        column_name, data_type, is_nullable, column_default = column
        default_str = str(column_default) if column_default else "None"
        print(f"{column_name:<25} {data_type:<20} {is_nullable:<10} {default_str}")
    
    cursor.close()
    conn.close()

if __name__ == "__main__":
    tables_to_check = ['master_sensors', 'master_channels']
    for table in tables_to_check:
        check_table_schema(table)
