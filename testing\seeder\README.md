# Database Seeder Script

This Python script implements a comprehensive database seeder for the ThingsBoard-based IoT system, following the plan outlined in `python script plan.txt`.

## 🚀 Features

- **Multi-tenant data seeding**: Creates tenants with complete hierarchical data
- **Configurable parameters**: Easily adjust the amount of data to generate
- **Transactional integrity**: Each tenant is seeded in a separate transaction
- **Error handling**: Comprehensive error handling with detailed logging
- **Database verification**: Includes scripts to verify seeded data

## 📁 Project Structure

```
testing/seeder/
├── main.py                 # Main seeder script
├── config.py              # Configuration parameters
├── database.py            # Database connection utilities
├── requirements.txt       # Python dependencies
├── .env                   # Database credentials
├── seeder/                # Seeder modules package
│   ├── __init__.py
│   ├── tenant_seeder.py   # Tenant creation
│   ├── user_seeder.py     # User creation
│   ├── factory_seeder.py  # Factory and location creation
│   ├── device_seeder.py   # Gateway, sensor, and channel creation
│   └── history_seeder.py  # Historical data generation
├── test_connection.py     # Database connection test
├── check_tables.py        # Table existence verification
├── check_schema.py        # Table schema inspection
└── verify_data.py         # Data verification script
```

## 🛠️ Setup and Installation

1. **Install dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

2. **Configure database credentials** in `.env`:
   ```
   SPRING_DATASOURCE_URL=********************************************
   SPRING_DATASOURCE_USERNAME=postgres
   SPRING_DATASOURCE_PASSWORD=postgres
   ```

3. **Adjust configuration** in `config.py`:
   - `NUM_TENANTS`: Number of tenants to create
   - `GATEWAYS_PER_TENANT`: Gateways per tenant
   - `SENSORS_PER_GATEWAY`: Sensors per gateway
   - `CHANNELS_PER_SENSOR`: Channels per sensor

## 🎯 Usage

### Run the main seeder:
```bash
python main.py
```

### Test database connection:
```bash
python test_connection.py
```

### Verify seeded data:
```bash
python verify_data.py
```

### Check table schemas:
```bash
python check_schema.py
```

## 📊 Data Generated

The script creates a complete hierarchical structure:

1. **Tenants** with unique titles and tenant numbers
2. **Users** (TENANT_ADMIN) for each tenant
3. **Factories** with hierarchical structure:
   - CAS Locations
   - Measurement Locations
   - Measurement Points
4. **IoT Infrastructure**:
   - Gateways (also stored as devices)
   - Sensors linked to gateways
   - Channels linked to sensors
   - Configuration mappings
5. **Historical Data** with random sensor readings

## 🧪 Test Results

Successfully tested with:
- ✅ 1 tenant created
- ✅ 1 user created
- ✅ 1 gateway with 2 sensors and 6 channels
- ✅ Complete factory hierarchy (2 CAS locations, 4 measurement locations, 8 measurement points)
- ✅ 930 historical data points generated
- ✅ All database constraints satisfied

## 🔧 Configuration for Production

For production use, adjust the following in `config.py`:

```python
# Production configuration example
NUM_TENANTS = 100
GATEWAYS_PER_TENANT = 2
SENSORS_PER_GATEWAY = 5
CHANNELS_PER_SENSOR = 10
```

And modify the history data generation period in `main.py`:
```python
# For full day of data
history_seeder.create_history_data(tenant_conn, tenant_id, all_channels, '2025-07-01', '2025-07-02')
```

## 🚨 Important Notes

- Each tenant is seeded in a separate transaction for data integrity
- The script uses UUID generation for all primary keys
- Database connections are properly managed and closed
- Error handling includes rollback functionality
- Historical data generation can be time-intensive for large datasets

## 🐛 Troubleshooting

1. **Connection issues**: Check database credentials in `.env`
2. **Missing tables**: Ensure the ThingsBoard database schema is properly initialized
3. **Constraint violations**: Check that all required tables have the expected schema
4. **Performance issues**: Reduce the amount of data generated for testing

## 📈 Performance Considerations

- History data generation is the most time-intensive operation
- Consider reducing the time range or frequency for large datasets
- Each tenant uses a separate database connection to avoid cursor conflicts
- Transactions are committed per tenant to ensure data consistency
