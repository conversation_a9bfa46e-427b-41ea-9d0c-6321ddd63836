# Database Seeder Script

This Python script implements a comprehensive database seeder for the ThingsBoard-based IoT system, creating complete tenant environments with all necessary data structures.

## 🚀 Features

- **Multi-tenant data seeding**: Creates tenants with complete hierarchical data
- **Multiple users per tenant**: 1 tenant owner + configurable number of tenant users
- **Factory hierarchy mapping**: Channels properly mapped to measurement points
- **Rule chain integration**: Complete rule chains and rule nodes for each tenant
- **Proper naming conventions**: S331 gateways, S401/S430 sensors with logical naming
- **Permission management**: Detailed role-based permissions for owners and users
- **Configurable parameters**: Easily adjust the amount of data to generate
- **Transactional integrity**: Each tenant is seeded in a separate transaction
- **Error handling**: Comprehensive error handling with detailed logging
- **Database verification**: Includes scripts to verify seeded data and consistency

## 📁 Project Structure

```
testing/seeder/
├── main.py                    # Main seeder script
├── config.py                  # Configuration parameters and constants
├── database.py                # Database connection utilities
├── requirements.txt           # Python dependencies
├── .env                       # Database credentials
├── seeder/                    # Seeder modules package
│   ├── __init__.py
│   ├── tenant_seeder.py       # Tenant creation with admin details
│   ├── user_seeder.py         # Multiple user creation (owner + users)
│   ├── factory_seeder.py      # Factory hierarchy creation
│   ├── device_seeder.py       # Gateway, sensor, and channel creation
│   ├── history_seeder.py      # Historical data generation
│   ├── license_seeder.py      # License management
│   ├── permission_seeder.py   # Role-based permissions
│   ├── device_profile_seeder.py # Device profile creation
│   ├── credentials_seeder.py  # User and device credentials
│   ├── rule_chain_seeder.py   # Rule chain creation
│   └── rule_node_seeder.py    # Rule node creation
├── test_connection.py         # Database connection test
├── check_tables.py            # Table existence verification
├── check_schema.py            # Table schema inspection
├── verify_data.py             # Data verification and consistency check
├── cleanup_test_data.py       # Test data cleanup utility
└── IMPLEMENTATION_SUMMARY.md  # Detailed implementation summary
```

## 🛠️ Setup and Installation

1. **Install dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

2. **Adjust configuration** in `config.py`:
   - `NUM_TENANTS`: Number of tenants to create
   - `GATEWAYS_PER_TENANT`: Gateways per tenant
   - `SENSORS_PER_GATEWAY`: Sensors per gateway
   - `CHANNELS_PER_SENSOR`: Channels per sensor
   - `USERS_PER_TENANT`: Number of additional users (beyond owner)
   - `FACTORIES_PER_TENANT`: Number of factories per tenant
   - `CHANNELS_PER_MEASUREMENT_POINT`: Channels mapped to each measurement point

## 🎯 Usage

### Run the main seeder:
```bash
python main.py
```

### Test database connection:
```bash
python test_connection.py
```

### Verify seeded data:
```bash
python verify_data.py
```

### Check table schemas:
```bash
python check_schema.py
```

### Clean up test data:
```bash
python cleanup_test_data.py
```

## 📊 Data Generated

The script creates a complete hierarchical structure:

1. **Tenants** with admin details, regions, and proper search text
2. **Users** with role-based structure:
   - 1 Tenant Owner (TENANT_ADMIN authority)
   - Multiple Tenant Users (TENANT_ADMIN authority with Standard role)
   - Complete user credentials for all users
3. **Factories** with complete hierarchical structure:
   - CAS Locations
   - Measurement Locations
   - Measurement Points (6 channels per measurement point)
4. **IoT Infrastructure** with proper naming:
   - Gateways: S331-{serial} format
   - Sensors: S401/S430 models with {model}/{gateway_serial} naming
   - Channels: Logical names (Temperature, Pressure, etc.) with proper units
   - Configuration mappings with factory hierarchy (factory_id, cas_location_id, etc.)
5. **Device Management**:
   - Device profiles with proper configuration
   - Device and user credentials
   - Master gateway records with firmware/hardware info
6. **Permission System**:
   - Owner permissions: 11 features with Administrator role
   - User permissions: 9 features with User role
   - Proper permission flags and parameters
7. **Rule Engine**:
   - Root Rule Chain for each tenant
   - 7 Rule Nodes: Device Profile, Message Type Switch, Save Timeseries, Save Client Attributes, Log nodes, RPC Call Request
8. **License Management**:
   - License records with validity periods
   - License type management
9. **Historical Data** with proper entity_id and key_id mapping
10. **TS KV Dictionary** entries for sensor name mapping

## 🧪 Test Results

Successfully tested with current configuration:
- ✅ **1 Tenant** with complete admin details (name, email, region, channels, users)
- ✅ **4 Users** (1 owner + 3 users) with proper authorities and credentials
- ✅ **2 Factories** with complete hierarchy
- ✅ **4 Measurement Points** total (2 per factory)
- ✅ **1 Gateway** (S331-20001000) with proper device profile and credentials
- ✅ **2 Sensors** (S401/20001000, S430/20001000) with ts_kv_dictionary entries
- ✅ **10 Channels** properly mapped to measurement points with factory hierarchy
- ✅ **38 Permission Records** (11 for owner + 9×3 for users) with correct structure
- ✅ **200 History Data Points** with proper entity_id and key_id consistency
- ✅ **1 Rule Chain** with 7 rule nodes (Device Profile, Message Switch, Save nodes, Log nodes, RPC)
- ✅ **License Management** with active licenses and type management
- ✅ **All database constraints** satisfied with proper foreign key relationships

## 🔧 Configuration for Production

For production use, adjust the following in `config.py`:

```python
# Production configuration example
NUM_TENANTS = 100
GATEWAYS_PER_TENANT = 2
SENSORS_PER_GATEWAY = 5
CHANNELS_PER_SENSOR = 6  # 6 channels per measurement point
USERS_PER_TENANT = 3     # Additional users beyond owner
FACTORIES_PER_TENANT = 2
CAS_LOCATIONS_PER_FACTORY = 2
MEASUREMENT_LOCATIONS_PER_CAS = 2
MEASUREMENT_POINTS_PER_LOCATION = 2
CHANNELS_PER_MEASUREMENT_POINT = 6
```

And modify the history data generation period in `main.py`:
```python
# For full day of data
history_seeder.create_history_data(tenant_conn, tenant_id, channel_data_list, '2025-07-01', '2025-07-02')
```

## 🚨 Important Notes

- **Transaction integrity**: Each tenant is seeded in a separate transaction for data integrity
- **UUID generation**: All primary keys use proper UUID generation
- **Connection management**: Database connections are properly managed and closed
- **Error handling**: Comprehensive error handling with rollback functionality
- **Naming conventions**: Proper S331/S401/S430 naming with logical channel names
- **Factory mapping**: Channels are properly mapped to measurement points with complete hierarchy
- **Permission structure**: Individual permission records per feature with correct schema
- **Rule engine**: Complete rule chain structure for each tenant
- **Key ID management**: Uses PostgreSQL sequence for ts_kv_dictionary key_id generation
- **Data consistency**: All foreign key relationships properly maintained

## 🐛 Troubleshooting

1. **Connection issues**: Check database credentials in `.env`
2. **Missing tables**: Ensure the ThingsBoard database schema is properly initialized
3. **Constraint violations**: Check that all required tables have the expected schema
4. **Permission errors**: Verify config_tb_user_role_permission table schema matches expected structure
5. **Rule chain issues**: Ensure rule_chain and rule_node tables exist
6. **Key ID conflicts**: The script uses PostgreSQL sequences for ts_kv_dictionary key generation
7. **Performance issues**: Reduce the amount of data generated for testing
8. **Cleanup issues**: Use `cleanup_test_data.py` to remove test data before re-running

## 📈 Performance Considerations

- **History data generation** is the most time-intensive operation
- **Rule chain creation** adds minimal overhead (7 nodes per tenant)
- **Permission creation** scales with number of users (11 + 9×users records per tenant)
- **Channel mapping** to measurement points requires proper factory hierarchy
- **Key ID generation** uses database sequences for optimal performance
- **Consider reducing** the time range or frequency for large datasets
- **Separate connections** per tenant to avoid cursor conflicts
- **Transaction per tenant** ensures data consistency and rollback capability
- **Cleanup utility** available for removing test data efficiently

## 🆕 Latest Features

### Channel-Factory Mapping
- Channels are now properly mapped to measurement points
- Each record in `config_tenant_gateway_sensors_channels` includes:
  - `factory_id`, `cas_location_id`, `measurement_location_id`, `measurement_point_id`
  - 6 channels per measurement point as specified

### Multi-User Support
- **1 Tenant Owner** with `TENANT_ADMIN` authority and `Owner` role type
- **Configurable number of Tenant Users** with `TENANT_ADMIN` authority and `Standard` role type
- Complete user credentials for all users

### Enhanced Permission System
- **Individual permission records** per feature (not combined)
- **Owner permissions**: 11 features with `Administrator` role and `All` parameter
- **User permissions**: 9 features with `User` role and `hhh` parameter
- Proper permission flags: `public_create`, `public_edit`, `public_delete`, `public_view`, `private`, `disable_flag`

### Rule Engine Integration
- **Root Rule Chain** created for each tenant
- **7 Rule Nodes** per chain:
  - Device Profile Node (entry point)
  - Message Type Switch
  - Save Timeseries & Save Client Attributes
  - Log RPC from Device & Log Other
  - RPC Call Request
- Proper node configuration with correct types and layouts

### Improved Data Consistency
- **PostgreSQL sequences** for ts_kv_dictionary key_id generation
- **Proper entity_id mapping** for all device-related tables
- **Complete foreign key relationships** maintained
- **Logical naming conventions** for all entities
