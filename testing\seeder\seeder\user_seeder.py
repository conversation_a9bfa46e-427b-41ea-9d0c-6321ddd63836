import uuid
import time

def create_user(conn, tenant_id):
    """Creates a new user for a given tenant and returns the user_id."""
    cursor = conn.cursor()
    user_id = str(uuid.uuid4())
    created_time = int(time.time() * 1000)
    
    # Generate a unique email and name for the user
    email = f"user_{created_time}@example.com"
    first_name = f"User_{created_time}"
    
    authority = 'TENANT_ADMIN'
    
    query = """
    INSERT INTO tb_user (id, created_time, tenant_id, email, first_name, authority)
    VALUES (%s, %s, %s, %s, %s, %s)
    """
    cursor.execute(query, (user_id, created_time, tenant_id, email, first_name, authority))
    cursor.close()
    
    print(f"Created user with ID: {user_id} for tenant: {tenant_id}")
    return user_id
