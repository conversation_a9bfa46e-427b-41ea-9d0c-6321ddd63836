import os
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

# Database credentials from .env file
DB_NAME = "thingsboard"
DB_USER = "postgres"
DB_PASSWORD = "postgres"
DB_HOST = "localhost"
DB_PORT = "5432"

# Number of tenants to create (reduced for testing)
NUM_TENANTS = 1

# Data generation parameters (reduced for testing)
GATEWAYS_PER_TENANT = 1
SENSORS_PER_GATEWAY = 2
CHANNELS_PER_SENSOR = 3
USERS_PER_TENANT = 1
FACTORIES_PER_TENANT = 1
CAS_LOCATIONS_PER_FACTORY = 2
MEASUREMENT_LOCATIONS_PER_CAS = 2
MEASUREMENT_POINTS_PER_LOCATION = 2
