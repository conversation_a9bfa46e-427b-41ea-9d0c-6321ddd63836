import uuid
import time

def create_tenant(conn):
    """Creates a new tenant and returns the tenant_id."""
    cursor = conn.cursor()
    tenant_id = str(uuid.uuid4())
    created_time = int(time.time() * 1000)
    
    # Generate a unique tenant title
    title = f"Tenant_{created_time}"
    
    # Generate a unique tenant number
    tenant_no = str(int(time.time()))
    
    additional_info = '{"description":"","homeDashboardId":null,"homeDashboardHideToolbar":true}'
    tenant_profile_id = '790f9240-5c2e-11f0-8845-07378cc40f59' # Default tenant profile
    
    query = """
    INSERT INTO tenant (id, created_time, title, tenant_no, additional_info, tenant_profile_id)
    VALUES (%s, %s, %s, %s, %s, %s)
    """
    cursor.execute(query, (tenant_id, created_time, title, tenant_no, additional_info, tenant_profile_id))
    cursor.close()
    
    print(f"Created tenant with ID: {tenant_id}")
    return tenant_id
