import uuid
import time
import config

def create_tenant(conn):
    """Creates a new tenant and returns the tenant_id and tenant_no."""
    cursor = conn.cursor()
    tenant_id = str(uuid.uuid4())
    created_time = int(time.time() * 1000)

    # Generate a unique tenant title
    title = f"Tenant_{created_time}"

    # Generate a sequential tenant number
    tenant_serial = config.get_next_tenant_serial()
    tenant_no = str(tenant_serial)
    tenant_admin = f"TenantAdmin_{created_time}"
    tenant_email = f"Tenant_{created_time}@example.com"

    additional_info = '{"description":"","homeDashboardId":null,"homeDashboardHideToolbar":true}'

    query = """
    INSERT INTO tenant (id, created_time, title, tenant_no, additional_info, tenant_profile_id, admin_name, admin_email)
    VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
    """
    cursor.execute(query, (tenant_id, created_time, title, tenant_no, additional_info, config.DEFAULT_TENANT_PROFILE_ID, tenant_admin, tenant_email))
    cursor.close()

    print(f"Created tenant with ID: {tenant_id}, tenant_no: {tenant_no}")
    return tenant_id, tenant_no, title
