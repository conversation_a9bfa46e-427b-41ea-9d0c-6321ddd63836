import uuid
import time
import config

def create_user_role_permissions(conn, tenant_id, user_id):
    """Creates role permissions for a given user."""
    cursor = conn.cursor()

    # Create a single permission record with all features
    permission_id = str(uuid.uuid4())
    created_time = int(time.time() * 1000)

    # Join all features into a single string
    feature_list = ','.join(config.PERMISSION_FEATURES)

    query = """
    INSERT INTO config_tb_user_role_permission
    (id, created_time, feature_list, role, role_type, owner_flag, use_custom_flag)
    VALUES (%s, %s, %s, %s, %s, %s, %s)
    """

    cursor.execute(query, (
        permission_id,
        created_time,
        feature_list,
        'Administrator',
        'Owner',
        'Yes',
        'No'  # use_custom_flag
    ))

    cursor.close()
    print(f"Created role permissions for user: {user_id}")
