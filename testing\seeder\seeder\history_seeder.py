import uuid
import random
from datetime import datetime, timedelta

def create_history_data(conn, tenant_id, channels, start_date, end_date):
    """Creates history data for all channels of a tenant."""
    cursor = conn.cursor()

    # For testing, create just 10 minutes of data
    start_dt = datetime.strptime(start_date, '%Y-%m-%d')
    end_dt = start_dt + timedelta(minutes=10)  # Just 10 minutes of data

    data_points_created = 0
    current_dt = start_dt
    while current_dt < end_dt:
        for channel_id in channels:
            history_id = str(uuid.uuid4())
            created_time = int(current_dt.timestamp() * 1000)
            data_value = str(random.uniform(0, 100))

            cursor.execute(
                "INSERT INTO history_report_data (id, created_time, entity_id, data_value) VALUES (%s, %s, %s, %s)",
                (history_id, created_time, channel_id, data_value)
            )
            data_points_created += 1

        current_dt += timedelta(seconds=30)  # Data every 30 seconds instead of 5

    cursor.close()

    print(f"Created {data_points_created} history data points for tenant: {tenant_id}")
