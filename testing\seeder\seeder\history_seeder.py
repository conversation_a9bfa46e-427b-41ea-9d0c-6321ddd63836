import uuid
import random
from datetime import datetime, timedelta

def create_history_data(conn, tenant_id, channel_data_list, start_date, end_date):
    """Creates history data for all channels of a tenant.

    Args:
        conn: Database connection
        tenant_id: Tenant ID
        channel_data_list: List of dicts with channel info from device_seeder
        start_date: Start date string
        end_date: End date string
    """
    cursor = conn.cursor()

    # For testing, create just 10 minutes of data
    start_dt = datetime.strptime(start_date, '%Y-%m-%d')
    end_dt = start_dt + timedelta(minutes=10)  # Just 10 minutes of data

    data_points_created = 0
    current_dt = start_dt
    while current_dt < end_dt:
        for channel_data in channel_data_list:
            history_id = str(uuid.uuid4())
            created_time = int(current_dt.timestamp() * 1000)
            data_value = str(random.randint(-20, 100))

            # Use the correct entity_id (device_id) and key_id from the channel data
            cursor.execute("""
                INSERT INTO history_report_data
                (id, created_time, entity_id, key_id, sensor_name, channel_name, data_value)
                VALUES (%s, %s, %s, %s, %s, %s, %s)
            """, (
                history_id,
                created_time,
                channel_data['device_id'],  # entity_id is the gateway device_id
                channel_data['key_id'],     # key_id from ts_kv_dictionary
                f"\"{channel_data['sensor_name']}\"",
                channel_data['channel_name'],
                data_value
            ))
            data_points_created += 1

        current_dt += timedelta(seconds=30)  # Data every 30 seconds instead of 5

    cursor.close()

    print(f"Created {data_points_created} history data points for tenant: {tenant_id}")
