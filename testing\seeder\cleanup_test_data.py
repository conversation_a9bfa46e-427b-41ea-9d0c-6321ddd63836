from database import get_db_connection

def cleanup_test_data():
    """Clean up test data created by the seeder."""
    conn = get_db_connection()
    if not conn:
        return
    
    cursor = conn.cursor()
    
    print("🧹 Cleaning up test data...")
    
    try:
        # Delete in reverse order of creation to respect foreign keys
        
        # History data
        cursor.execute("DELETE FROM history_report_data WHERE sensor_name LIKE 'S40%/%'")
        history_deleted = cursor.rowcount
        print(f"🗑️ Deleted {history_deleted} history records")
        
        # Configuration mappings
        cursor.execute("DELETE FROM config_tenant_gateway_sensors_channels WHERE tenant_id IN (SELECT id FROM tenant WHERE title LIKE 'Tenant_%')")
        config_deleted = cursor.rowcount
        print(f"🗑️ Deleted {config_deleted} configuration mappings")
        
        # Channels
        cursor.execute("DELETE FROM master_channels WHERE tenant_id IN (SELECT id FROM tenant WHERE title LIKE 'Tenant_%')")
        channels_deleted = cursor.rowcount
        print(f"🗑️ Deleted {channels_deleted} channels")
        
        # TS KV Dictionary entries
        cursor.execute("DELETE FROM ts_kv_dictionary WHERE key LIKE 'S40%/%'")
        ts_kv_deleted = cursor.rowcount
        print(f"🗑️ Deleted {ts_kv_deleted} ts_kv_dictionary entries")
        
        # Sensors
        cursor.execute("DELETE FROM master_sensors WHERE tenant_id IN (SELECT id FROM tenant WHERE title LIKE 'Tenant_%')")
        sensors_deleted = cursor.rowcount
        print(f"🗑️ Deleted {sensors_deleted} sensors")
        
        # Master gateways
        cursor.execute("DELETE FROM master_gateway WHERE tenant_id IN (SELECT id FROM tenant WHERE title LIKE 'Tenant_%')")
        master_gw_deleted = cursor.rowcount
        print(f"🗑️ Deleted {master_gw_deleted} master gateway records")
        
        # Device credentials
        cursor.execute("DELETE FROM device_credentials WHERE device_id IN (SELECT id FROM device WHERE name LIKE 'S331-%')")
        device_creds_deleted = cursor.rowcount
        print(f"🗑️ Deleted {device_creds_deleted} device credentials")
        
        # Gateways
        cursor.execute("DELETE FROM gateway WHERE tenant_id IN (SELECT id FROM tenant WHERE title LIKE 'Tenant_%')")
        gateways_deleted = cursor.rowcount
        print(f"🗑️ Deleted {gateways_deleted} gateway records")
        
        # Devices
        cursor.execute("DELETE FROM device WHERE name LIKE 'S331-%'")
        devices_deleted = cursor.rowcount
        print(f"🗑️ Deleted {devices_deleted} devices")
        
        # Factory hierarchy
        cursor.execute("DELETE FROM config_measurement_point WHERE tenant_id IN (SELECT id FROM tenant WHERE title LIKE 'Tenant_%')")
        mp_deleted = cursor.rowcount
        print(f"🗑️ Deleted {mp_deleted} measurement points")
        
        cursor.execute("DELETE FROM config_measurement_location WHERE tenant_id IN (SELECT id FROM tenant WHERE title LIKE 'Tenant_%')")
        ml_deleted = cursor.rowcount
        print(f"🗑️ Deleted {ml_deleted} measurement locations")
        
        cursor.execute("DELETE FROM config_cas_location WHERE tenant_id IN (SELECT id FROM tenant WHERE title LIKE 'Tenant_%')")
        cas_deleted = cursor.rowcount
        print(f"🗑️ Deleted {cas_deleted} CAS locations")
        
        cursor.execute("DELETE FROM config_tenant_factory WHERE tenant_id IN (SELECT id FROM tenant WHERE title LIKE 'Tenant_%')")
        factories_deleted = cursor.rowcount
        print(f"🗑️ Deleted {factories_deleted} factories")
        
        # Permissions
        cursor.execute("DELETE FROM config_tb_user_role_permission WHERE id IN (SELECT p.id FROM config_tb_user_role_permission p JOIN tenant t ON p.created_time > t.created_time WHERE t.title LIKE 'Tenant_%')")
        permissions_deleted = cursor.rowcount
        print(f"🗑️ Deleted {permissions_deleted} permissions")
        
        # Licenses
        cursor.execute("DELETE FROM license_type_management WHERE tenant_id IN (SELECT id FROM tenant WHERE title LIKE 'Tenant_%')")
        license_type_deleted = cursor.rowcount
        print(f"🗑️ Deleted {license_type_deleted} license type records")
        
        cursor.execute("DELETE FROM license_management WHERE tenant_id IN (SELECT id FROM tenant WHERE title LIKE 'Tenant_%')")
        license_deleted = cursor.rowcount
        print(f"🗑️ Deleted {license_deleted} license records")
        
        # Device profiles
        cursor.execute("DELETE FROM device_profile WHERE tenant_id IN (SELECT id FROM tenant WHERE title LIKE 'Tenant_%')")
        profiles_deleted = cursor.rowcount
        print(f"🗑️ Deleted {profiles_deleted} device profiles")
        
        # User credentials
        cursor.execute("DELETE FROM user_credentials WHERE user_id IN (SELECT id FROM tb_user WHERE email LIKE '<EMAIL>')")
        user_creds_deleted = cursor.rowcount
        print(f"🗑️ Deleted {user_creds_deleted} user credentials")
        
        # Users
        cursor.execute("DELETE FROM tb_user WHERE email LIKE '<EMAIL>'")
        users_deleted = cursor.rowcount
        print(f"🗑️ Deleted {users_deleted} users")
        
        # Tenants
        cursor.execute("DELETE FROM tenant WHERE title LIKE 'Tenant_%'")
        tenants_deleted = cursor.rowcount
        print(f"🗑️ Deleted {tenants_deleted} tenants")
        
        conn.commit()
        print("✅ Cleanup completed successfully!")
        
    except Exception as e:
        print(f"❌ Error during cleanup: {e}")
        conn.rollback()
    finally:
        cursor.close()
        conn.close()

if __name__ == "__main__":
    cleanup_test_data()
