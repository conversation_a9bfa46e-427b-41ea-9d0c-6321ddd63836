import uuid
import time
import config

def create_devices(conn, tenant_id):
    """Creates gateways, sensors, and channels for a given tenant."""
    cursor = conn.cursor()
    all_channels = []
    
    for _ in range(config.GATEWAYS_PER_TENANT):
        gateway_id = str(uuid.uuid4())
        created_time = int(time.time() * 1000)
        gateway_name = f"Gateway_{created_time}"
        
        # Insert into device table first
        device_id = gateway_id
        device_profile_id = 'c51cd780-5c31-11f0-9a84-43b4f1c00692' # Default device profile
        device_data = '{"configuration": {"type": "DEFAULT"}, "transportConfiguration": {"type": "DEFAULT"}}'
        
        cursor.execute(
            "INSERT INTO device (id, created_time, tenant_id, device_profile_id, device_data, name, type) VALUES (%s, %s, %s, %s, %s, %s, %s)",
            (device_id, created_time, tenant_id, device_profile_id, device_data, gateway_name, 'gateway')
        )
        
        # Insert into gateway table
        cursor.execute(
            "INSERT INTO gateway (id, created_time, tenant_id, gateway_name) VALUES (%s, %s, %s, %s)",
            (gateway_id, created_time, tenant_id, gateway_name)
        )
        
        for _ in range(config.SENSORS_PER_GATEWAY):
            sensor_id = str(uuid.uuid4())
            sensor_name = f"Sensor_{int(time.time() * 1000)}"
            
            # Insert into master_sensors table
            cursor.execute(
                "INSERT INTO master_sensors (id, created_time, inserted_time, tenant_id, entity_id, sensors_name, gw) VALUES (%s, %s, NOW(), %s, %s, %s, %s)",
                (sensor_id, int(time.time() * 1000), tenant_id, gateway_id, sensor_name, gateway_id)
            )
            
            for _ in range(config.CHANNELS_PER_SENSOR):
                channel_id = str(uuid.uuid4())
                channel_name = f"Channel_{int(time.time() * 1000)}"
                
                # Insert into master_channels table
                cursor.execute(
                    "INSERT INTO master_channels (id, created_time, inserted_time, tenant_id, entity_id, channel_name) VALUES (%s, %s, NOW(), %s, %s, %s)",
                    (channel_id, int(time.time() * 1000), tenant_id, sensor_id, channel_name)
                )
                all_channels.append(channel_id)
                
                # Insert into config_tenant_gateway_sensors_channels
                config_id = str(uuid.uuid4())
                cursor.execute(
                    "INSERT INTO config_tenant_gateway_sensors_channels (id, created_time, tenant_id, gateway_id, sensors_id, channel_id) VALUES (%s, %s, %s, %s, %s, %s)",
                    (config_id, int(time.time() * 1000), tenant_id, gateway_id, sensor_id, channel_id)
                )

    cursor.close()
    
    print(f"Created gateways, sensors, and channels for tenant: {tenant_id}")
    return all_channels
