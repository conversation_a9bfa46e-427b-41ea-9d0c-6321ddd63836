import uuid
import time
import random
import config
from . import credentials_seeder

def create_devices(conn, tenant_id, device_profile_id, tenant_no, tenant_name, measurement_points):
    """Creates gateways, sensors, and channels for a given tenant.

    Args:
        measurement_points: List of measurement point hierarchy information

    Returns a list of dictionaries with channel information for history seeder:
    [{'channel_id': ..., 'device_id': ..., 'key_id': ..., 'sensor_name': ..., 'channel_name': ...}]
    """
    cursor = conn.cursor()
    all_channel_data = []

    for _ in range(config.GATEWAYS_PER_TENANT):
        # Generate gateway serial number and name
        gateway_serial = config.get_next_gateway_serial()
        gateway_serial_str = str(gateway_serial)
        gateway_name = f"{config.GATEWAY_MODEL}-{gateway_serial_str}"

        # Create gateway device
        device_id = str(uuid.uuid4())
        created_time = int(time.time() * 1000)

        device_data = '{"configuration": {"type": "DEFAULT"}, "transportConfiguration": {"type": "DEFAULT"}}'

        # Insert into device table
        cursor.execute("""
            INSERT INTO device (id, created_time, tenant_id, customer_id, device_profile_id,
                               device_data, name, type, model, sn, search_text)
            VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
        """, (device_id, created_time, tenant_id, config.PUBLIC_CUSTOMER_ID, device_profile_id,
              device_data, gateway_name, 'default', config.GATEWAY_MODEL, gateway_serial_str, gateway_name.lower()))

        # Insert into gateway table (legacy)
        cursor.execute("""
            INSERT INTO gateway (id, created_time, tenant_id, gateway_name, tenant_no, tenant_name, gateway_status, search_text, inserted_date_time)
            VALUES (%s, %s, %s, %s, %s, %s, %s, %s, NOW())
        """, (device_id, created_time, tenant_id, gateway_name, tenant_no, tenant_name, 'Online', gateway_name.lower()))

        # Insert into master_gateway table
        cursor.execute("""
            INSERT INTO master_gateway (id, created_time, inserted_time, tenant_id, entity_id,
                                       gateway_name, gateway_serial_no, g_type, g_fw, g_hw, teanant_no, customer_id)
            VALUES (%s, %s, NOW(), %s, %s, %s, %s, %s, %s, %s, %s, %s)
        """, (str(uuid.uuid4()), created_time, tenant_id, device_id, f"\"{config.GATEWAY_MODEL}\"",
              f"\"{gateway_serial_str}\"", '"SUTOgateway"', '"1.39"', '"1.1"', f"\"{tenant_no}\"", config.PUBLIC_CUSTOMER_ID))

        # Create device credentials
        credentials_seeder.create_device_credentials(conn, device_id)

        # Create sensors for this gateway
        sensor_data = create_sensors_for_gateway(conn, tenant_id, device_id, gateway_serial_str, measurement_points)
        all_channel_data.extend(sensor_data)

    cursor.close()

    print(f"Created {config.GATEWAYS_PER_TENANT} gateways with sensors and channels for tenant: {tenant_id}")
    return all_channel_data

def create_sensors_for_gateway(conn, tenant_id, gateway_device_id, gateway_serial_str, measurement_points):
    """Creates sensors for a specific gateway."""
    cursor = conn.cursor()
    sensor_channel_data = []

    for i in range(config.SENSORS_PER_GATEWAY):
        # Generate sensor name and model - make each sensor unique
        sensor_model = config.SENSOR_MODELS[i % len(config.SENSOR_MODELS)]
        sensor_name = f"{sensor_model}/{gateway_serial_str}"  # Add sensor index for uniqueness
        gateway_name = f"{config.GATEWAY_MODEL}/{gateway_serial_str}"  # Add sensor index for uniqueness

        sensor_id = str(uuid.uuid4())
        created_time = int(time.time() * 1000)

        # Insert into master_sensors table
        cursor.execute("""
            INSERT INTO master_sensors (id, created_time, inserted_time, tenant_id, entity_id,
                                       sensors_name, sensor_serial_no, s_type, gw, customer_id, s_fw, s_hw)
            VALUES (%s, %s, NOW(), %s, %s, %s, %s, %s, %s, %s, %s, %s)
        """, (sensor_id, created_time, tenant_id, gateway_device_id, f"\"{sensor_model}\"", f"\"{gateway_serial_str}\"",
              '"SUTOsensor"', f"\"{gateway_name}\"", config.PUBLIC_CUSTOMER_ID, f"\"2.7\"", f"\"1.0\""))

        # Create entry in ts_kv_dictionary
        key_id = config.get_next_key_id(conn)
        try:
            cursor.execute("""
                INSERT INTO ts_kv_dictionary (key, key_id)
                VALUES (%s, %s)
            """, (sensor_name, key_id))
        except Exception as e:
            if "duplicate key value violates unique constraint" in str(e):
                print(f"Warning: Duplicate key for ts_kv_dictionary: {sensor_name}. Ignoring.")

        print(f"Created sensor {sensor_name} with key_id {key_id}")

        # Create channels for this sensor
        channel_data = create_channels_for_sensor(conn, tenant_id, gateway_device_id, sensor_id,
                                                 sensor_name, key_id, measurement_points)
        sensor_channel_data.extend(channel_data)

    cursor.close()
    return sensor_channel_data

def create_channels_for_sensor(conn, tenant_id, gateway_device_id, sensor_id, sensor_name, key_id, measurement_points):
    """Creates channels for a specific sensor, mapping them to measurement points."""
    cursor = conn.cursor()
    channel_data = []

    # Get available channel names and shuffle to avoid duplicates
    available_channels = config.unique_channel_names.copy()
    random.shuffle(available_channels)

    for i in range(min(config.CHANNELS_PER_SENSOR, len(available_channels))):
        channel_name = available_channels[i]
        unit_id = random.choice(config.CHANNEL_UNITS_MAP[channel_name])

        channel_id = str(uuid.uuid4())
        created_time = int(time.time() * 1000)
        resolution = random.randint(0, 4)


        # Insert into master_channels table
        cursor.execute("""
            INSERT INTO master_channels (id, created_time, inserted_time, tenant_id, entity_id,
                                        channel_name, unit_id, customer_id, resolution, attribute_key)
            VALUES (%s, %s, NOW(), %s, %s, %s, %s, %s, %s, %s)
        """, (channel_id, created_time, tenant_id, gateway_device_id, channel_name, unit_id, config.PUBLIC_CUSTOMER_ID, resolution, sensor_name))

        # Insert into config_tenant_gateway_sensors_channels with factory hierarchy
        # Map each channel to a measurement point (cycling through available measurement points)
        measurement_point = measurement_points[i % len(measurement_points)]
        config_id = str(uuid.uuid4())
        cursor.execute("""
            INSERT INTO config_tenant_gateway_sensors_channels
            (id, created_time, tenant_id, gateway_id, sensors_id, channel_id, gateway_status,
             convert_unit_name, display_channel_name, factory_id, cas_location_id,
             measurement_location_id, measurement_point_id)
            VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
        """, (config_id, created_time, tenant_id, gateway_device_id, sensor_id, channel_id, 'true',
              unit_id, channel_name, measurement_point['factory_id'], measurement_point['cas_location_id'],
              measurement_point['measurement_location_id'], measurement_point['measurement_point_id']))

        # Store channel data for history seeder
        channel_data.append({
            'channel_id': channel_id,
            'device_id': gateway_device_id,
            'key_id': key_id,
            'sensor_name': sensor_name,
            'channel_name': channel_name
        })

        print(f"Created channel {channel_name} ({unit_id}) for sensor {sensor_name} -> MP {measurement_point['measurement_point_id']}")

    cursor.close()
    return channel_data
