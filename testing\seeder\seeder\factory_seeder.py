import uuid
import time
import config

def create_factories(conn, tenant_id, user_id):
    """Creates new factories and their components for a given tenant."""
    for _ in range(config.FACTORIES_PER_TENANT):
        create_factory(conn, tenant_id, user_id)

def create_factory(conn, tenant_id, user_id):
    """Creates a single factory and its components."""
    cursor = conn.cursor()
    factory_id = str(uuid.uuid4())
    created_time = int(time.time() * 1000)
    
    # Generate a unique factory name
    factory_name = f"Factory_{created_time}"
    
    query = """
    INSERT INTO config_tenant_factory (id, created_time, tenant_id, factory_name, created_by)
    VALUES (%s, %s, %s, %s, %s)
    """
    cursor.execute(query, (factory_id, created_time, tenant_id, factory_name, user_id))
    
    # Create CAS locations, measurement locations, and measurement points
    for i in range(config.CAS_LOCATIONS_PER_FACTORY):
        cas_location_id = str(uuid.uuid4())
        cas_location_name = f"CAS_Location_{i+1}"
        cursor.execute(
            "INSERT INTO config_cas_location (id, created_time, tenant_id, factory_id, cas_location_name, created_by) VALUES (%s, %s, %s, %s, %s, %s)",
            (cas_location_id, int(time.time() * 1000), tenant_id, factory_id, cas_location_name, user_id)
        )
        for j in range(config.MEASUREMENT_LOCATIONS_PER_CAS):
            measurement_location_id = str(uuid.uuid4())
            measurement_location_name = f"Measurement_Location_{j+1}"
            cursor.execute(
                "INSERT INTO config_measurement_location (id, created_time, tenant_id, factory_id, cas_location_id, measurement_location_name, created_by) VALUES (%s, %s, %s, %s, %s, %s, %s)",
                (measurement_location_id, int(time.time() * 1000), tenant_id, factory_id, cas_location_id, measurement_location_name, user_id)
            )
            for k in range(config.MEASUREMENT_POINTS_PER_LOCATION):
                measurement_point_id = str(uuid.uuid4())
                measurement_point_name = f"Measurement_Point_{k+1}"
                cursor.execute(
                    "INSERT INTO config_measurement_point (id, created_time, tenant_id, factory_id, cas_location_id, measurement_location_id, measurement_point_name, created_by) VALUES (%s, %s, %s, %s, %s, %s, %s, %s)",
                    (measurement_point_id, int(time.time() * 1000), tenant_id, factory_id, cas_location_id, measurement_location_id, measurement_point_name, user_id)
                )

    cursor.close()
    
    print(f"Created factory with ID: {factory_id} for tenant: {tenant_id}")
    return factory_id
